<!DOCTYPE html>
<html lang="en-gb" dir="ltr">
<head>
  <meta http-equiv="content-type" content="text/html; charset=utf-8">
  <title>Simple Science</title>
  <link href="/index.php?option=com_mailto&amp;tmpl=component&amp;template=protostar&amp;link=f6e385ba0099bc676602d3e03403c6c219cb2093" rel="canonical">
  <link href="/templates/protostar/favicon.ico" rel="shortcut icon" type="image/vnd.microsoft.icon">
  <link rel="stylesheet" href="templates/protostar/css/template.css" type="text/css">
  <script src="/media/system/js/mootools-core.js" type="text/javascript"></script>
  <script src="/media/system/js/core.js" type="text/javascript"></script>
  <script src="/media/jui/js/jquery.min.js" type="text/javascript"></script>
  <script src="/media/jui/js/jquery-noconflict.js" type="text/javascript"></script>
  <script src="/media/jui/js/jquery-migrate.min.js" type="text/javascript"></script>
  <script src="/media/jui/js/bootstrap.min.js" type="text/javascript"></script>
  <script type="text/javascript">
  function keepAlive() {  var myAjax = new Request({method: "get", url: "index.php"}).send();} window.addEvent("domready", function(){ keepAlive.periodical(840000); });
  </script>
</head>
<body class="contentpane modal">
  <div id="system-message-container">
    <div id="system-message"></div>
  </div>
  <script type="text/javascript">
        Joomla.submitbutton = function(pressbutton)
        {
                var form = document.getElementById('mailtoForm');

                // do field validation
                if (form.mailto.value == "" || form.from.value == "")
                {
                        alert('Please provide a valid email address.');
                        return false;
                }
                form.submit();
        }
  </script>
  <div id="mailto-window">
    <h2>Email this link to a friend.</h2>
    <div class="mailto-close">
      <a href="javascript:%20void%20window.close()" title="Close Window"><span>Close Window</span></a>
    </div>
    <form action="https://simplescience.info/index.php" id="mailtoForm" method="post" name="mailtoForm">
      <div class="formelm">
        <label for="mailto_field">Email to</label> <input type="text" id="mailto_field" name="mailto" class="inputbox" size="25" value="">
      </div>
      <div class="formelm">
        <label for="sender_field">Sender</label> <input type="text" id="sender_field" name="sender" class="inputbox" value="" size="25">
      </div>
      <div class="formelm">
        <label for="from_field">Your Email</label> <input type="text" id="from_field" name="from" class="inputbox" value="" size="25">
      </div>
      <div class="formelm">
        <label for="subject_field">Subject</label> <input type="text" id="subject_field" name="subject" class="inputbox" value="" size="25">
      </div>
      <p><button class="button" onclick="return Joomla.submitbutton('send');">Send</button> <button class="button" onclick="window.close();return false;">Cancel</button></p><input type="hidden" name="layout" value="default"> <input type="hidden" name="option" value="com_mailto"> <input type="hidden" name="task" value="send"> <input type="hidden" name="tmpl" value="component"> <input type="hidden" name="link" value="f6e385ba0099bc676602d3e03403c6c219cb2093"> <input type="hidden" name="4356a1e11c01235874fe9ebad3d86c4d" value="1">
    </form>
  </div>
</body>
</html>
