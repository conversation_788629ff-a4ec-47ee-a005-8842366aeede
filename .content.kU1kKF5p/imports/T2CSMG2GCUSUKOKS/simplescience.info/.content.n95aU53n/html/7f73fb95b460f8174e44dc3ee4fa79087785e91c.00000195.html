<?xml version="1.0" encoding="iso-8859-1"?>
<!DOCTYPE html>
<html>
<head>
  <title>SimpleScience.info - Levers are Machines -1</title>
  <meta name="title" content="Levers are Machines -1">
  <meta name="description" content="physics chemistry biology">
  <meta name="keywords" content="physics chemistry biology">
  <link rel="shortcut icon" href="/images/favicon.ico">
  <meta http-equiv="Content-Type" content="text/html;&gt;charset=iso-8859-1">
  <link rel="stylesheet" href="/templates/247portal-blue/css/template_css.css" type="text/css">
  <link rel="shortcut icon" href="/templates/247portal-blue/favicon.ico">
  <link rel="alternate" title="SimpleScience.info" href="/index2.php?option=com_rss&no_html=1" type="application/rss+xml">
  <script language="JavaScript" type="text/javascript">
    <!--
    function MM_reloadPage(init) {  //reloads the window if Nav4 resized
      if (init==true) with (navigator) {if ((appName=="Netscape")&&(parseInt(appVersion)==4)) {
        document.MM_pgW=innerWidth; document.MM_pgH=innerHeight; onresize=MM_reloadPage; }}
      else if (innerWidth!=document.MM_pgW || innerHeight!=document.MM_pgH) location.reload();
    }
    MM_reloadPage(true);
    //-->
  </script>
  <style type="text/css">
  <!--
  .Stil1 {
        font-size: xx-small;
        color: #FFFFFF;
  }
  -->
  </style>
</head>
<body onload="popUpAd()">
  <div>
    <a name="up" id="up"></a>
    <table width="870" height="20" border="0" align="center" cellpadding="0" cellspacing="0">
      <tr>
        <td align="center" class="mt">www.mamboteam.com</td>
      </tr>
    </table>
    <table width="870" border="0" align="center" cellpadding="0" cellspacing="0">
      <tr>
        <td><img src="/templates/247portal-blue/images/space.gif" width="770" height="1"></td>
      </tr>
      <tr>
        <td>
          <div class="background">
            <table width="100%" border="0" cellpadding="0" cellspacing="0" background="https://SimpleScience.info//templates/247portal-blue/images/center.jpg">
              <tr>
                <td width="26"><img src="/templates/247portal-blue/images/left.jpg" width="26"></td>
                <td class="title">
                  <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                      <td height="100" colspan="2" class="title">
                        <table width="100%" border="0" cellpadding="0" cellspacing="0">
                          <tr>
                            <td width="100%" style="padding-left:10px;padding-top:14px;">
                              <a href="/" title="SimpleScience.info">SimpleScience.info</a>
                            </td>
                            <td style="padding-left:10px;padding-top:14px;"><img style='border:3px solid white;' src='http://www.LiveRanks.com/images/LiveRanks_Certified_468_60.png'></td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                    <tr>
                      <td width="400" height="29">
                        <div id="search">
                          <form action="index.php" method="post">
                            <div align="left" class="search">
                              <input alt="search" class="inputbox" type="text" name="searchword" size="20" value="search..." onblur="if(this.value=='') this.value='search...';" onfocus="if(this.value=='search...') this.value='';">
                            </div><input type="hidden" name="option" value="search">
                          </form>
                        </div>
                      </td>
                      <td width="100%" height="29" valign="bottom" class="mainlevel-nav"></td>
                    </tr>
                  </table>
                </td>
                <td width="26"><img src="/templates/247portal-blue/images/right.jpg" width="26"></td>
              </tr>
            </table>
            <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
              <tr>
                <td width="11" height="25" background="https://SimpleScience.info//templates/247portal-blue/images/shadowl.jpg">
                  <div></div>
                </td>
                <td height="25" bgcolor="#F1F1F1" style="border-bottom: 1px solid #999999; border-top: 5px solid #FFFFFF;"><span class="pathway"><a href="index.php" class="pathway">Home</a> <img src="/images/M_images/arrow.png" alt="arrow"> <a href="index.php?option=com_content&amp;task=section&amp;id=6&amp;Itemid=54" class="pathway">Content Index</a> <img src="/images/M_images/arrow.png" alt="arrow"> <a href="index.php?option=com_content&amp;task=category&amp;sectionid=6&amp;id=22&amp;Itemid=54" class="pathway">Science category</a> <img src="/images/M_images/arrow.png" alt="arrow"> Levers are Machines -1</span></td>
                <td height="25" align="right" bgcolor="#F1F1F1" style="border-bottom: 1px solid #999999; border-top: 5px solid #FFFFFF;">
                  <div class="date">
                    Monday, 02 April 2012
                  </div>
                </td>
                <td width="11" height="25" align="right" background="https://SimpleScience.info//templates/247portal-blue/images/shadowr.jpg">&nbsp;</td>
              </tr>
            </table>
            <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
              <tr>
                <td valign="top" style="padding-left:8px; background-repeat: repeat-y;" background="https://SimpleScience.info//templates/247portal-blue/images/shadowl.jpg">&nbsp;</td>
                <td valign="top" style="background-repeat: repeat-y;" background="https://SimpleScience.info//templates/247portal-blue/images/lb.gif">
                  <div class="leftrow">
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <td>
                          <table width="100%" border="0" cellpadding="0" cellspacing="0">
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_frontpage&amp;Itemid=1" class="mainlevel">Home</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=section&amp;id=6&amp;Itemid=54" class="mainlevel" id="active_menu">Content Index</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=blogcategory&amp;id=24&amp;Itemid=48" class="mainlevel">Simple Science</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=blogcategory&amp;id=22&amp;Itemid=63" class="mainlevel">General Science</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=blogcategory&amp;id=23&amp;Itemid=49" class="mainlevel">General Knowledge</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=blogcategory&amp;id=25&amp;Itemid=50" class="mainlevel">Biographies</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=blogcategory&amp;id=3&amp;Itemid=61" class="mainlevel">Site Services</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=view&amp;id=49&amp;Itemid=55" class="mainlevel">About us</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_contact&amp;catid=12&amp;Itemid=65" class="mainlevel">Contact us</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="#" onclick="javascript: window.open('https://simplescience.info/forum/index.php?action=register&amp;Itemid=67', '', 'toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=780,height=550'); return false" class="mainlevel">Register on Forum</a>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <td>
                          <div style="float:center;width:120px;height:600px;margin:auto;padding:auto;">
                            <script type="text/javascript">
                            <!--
                            google_ad_client = "pub-2867192467939979";
                            google_alternate_ad_url = "http://www.alternateurl.com/show?memid=944&size=120x600"; 
                            google_alternate_color = "FFFFFF"; 
                            google_ad_width = 120; 
                            google_ad_height = 600; 
                            google_ad_format = "120x600_as"; 
                            google_ad_type = "text_image"; 
                            google_ad_channel = "1234399007"; 
                            google_color_border = ["F1F1F1","F1F1F1","F1F1F1","F1F1F1"]; 
                            google_color_bg = ["F1F1F1","F1F1F1","F1F1F1","F1F1F1"]; 
                            google_color_link = ["0033FF","0033FF","0033FF","0033FF"]; 
                            google_color_url = ["008000","008000","008000","008000"]; 
                            google_color_text = ["333333","333333","333333","333333"]; 
                            //--> 
                            </script> 
                            <script type="text/javascript" src="http://pagead2.googlesyndication.com/pagead/show_ads.js"></script>
                          </div>
                        </td>
                      </tr>
                    </table>
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <th valign="top">Search here</th>
                      </tr>
                      <tr>
                        <td>
                          <form method="get" action="http://www.google.com/custom" target="_top">
                            <table border="0" cellspacing="0" cellpadding="0" bgcolor="#F1F1F1">
                              <tr>
                                <td nowrap="nowrap" valign="top" align="left" height="32"><img src="http://www.google.com/logos/Logo_25wht.gif" border="0" alt="Google" align="middle"><br>
                                <input type="text" name="q" size="20" maxlength="255" value=""></td>
                              </tr>
                              <tr>
                                <td valign="top" align="left"><input type="submit" name="sa" value="Search"> <input type="hidden" name="client" value="pub-2867192467939979"> <input type="hidden" name="channel" value="1234399007"> <input type="hidden" name="ie" value="ISO-8859-1"> <input type="hidden" name="oe" value="ISO-8859-1"> <input type="hidden" name="cof" value="GALT:#008000;GL:1;DIV:#FFFFFF;VLC:0000CC;AH:center;BGC:F1F1F1;LBGC:FFFFFF;ALC:0000CC;LC:0000CC;T:333333;GFNT:7777CC;GIMP:7777CC;LH:41;LW:120;L:http://www.google.com/logos/Logo_40wht.gif;S:http://mambo.medspan.info;LP:1;FORID:1;"> <input type="hidden" name="hl" value="en"></td>
                              </tr>
                            </table>
                          </form>
                        </td>
                      </tr>
                    </table>
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <th valign="top">Popular Articles</th>
                      </tr>
                      <tr>
                        <td>
                          <ul class="mostread">
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=114&amp;Itemid=54" class="mostread">Kingdom of Bacteria</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=117&amp;Itemid=54" class="mostread">Levers are Machines -1</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=80&amp;Itemid=54" class="mostread">Breathing & Respiration</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=167&amp;Itemid=54" class="mostread">Karl Benz</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=103&amp;Itemid=54" class="mostread">Friction is a Force</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=76&amp;Itemid=54" class="mostread">Introducing forces</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=97&amp;Itemid=54" class="mostread">The Periodic Table. 1-18.</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=173&amp;Itemid=54" class="mostread">Burning is a chemical change</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=48&amp;Itemid=54" class="mostread">Laureate Marie Curie</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=175&amp;Itemid=54" class="mostread">Gene- The basic unit of heredity</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=158&amp;Itemid=54" class="mostread">2. Ch.cal.Relative Molecular Mass</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=44&amp;Itemid=54" class="mostread">Laureate R Faynman</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=116&amp;Itemid=54" class="mostread">Pulleys are Machines</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=51&amp;Itemid=54" class="mostread">Brilliant Halo from a CD</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=77&amp;Itemid=54" class="mostread">Ionic compounds</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=57&amp;Itemid=54" class="mostread">Laureate Rontgen</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=75&amp;Itemid=54" class="mostread">Heart & Circulation</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=45&amp;Itemid=54" class="mostread">Teasing a Friend</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=115&amp;Itemid=54" class="mostread">Force of Gravity</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=91&amp;Itemid=54" class="mostread">Apnea- A Sleep Disorder.</a>
                            </li>
                          </ul>
                        </td>
                      </tr>
                    </table>
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <th valign="top">Make a Payment</th>
                      </tr>
                      <tr>
                        <td>
                          <script language="javascript" type="text/javascript">
                          function iFrameHeight() {
                          var h = 0;
                          if ( !document.all ) {
                          h = document.getElementById('blockrandom').contentDocument.height;
                          document.getElementById('blockrandom').style.height = h + 60 + 'px';
                          } else if( document.all ) {
                          h = document.frames('blockrandom').document.body.scrollHeight;
                          document.all.blockrandom.style.height = h + 20 + 'px';
                          }
                          }
                          </script> <iframe id="blockrandom" src="/new_code/donate.html" width="100%" height="80" scrolling="no" align="top" frameborder="0" class="wrapper" name="blockrandom"></iframe>
                        </td>
                      </tr>
                    </table>
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <th valign="top">Statistics</th>
                      </tr>
                      <tr>
                        <td><strong>Visitors:</strong> 115976</td>
                      </tr>
                    </table>
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <th valign="top">Login Form</th>
                      </tr>
                      <tr>
                        <td>
                          <form action="index.php" method="post" name="login" id="login">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0" align="center">
                              <tr>
                                <td>Username<br>
                                <input name="username" type="text" class="inputbox" alt="username" size="10"><br>
                                Password<br>
                                <input type="password" name="passwd" class="inputbox" size="10" alt="password"><br>
                                <input type="checkbox" name="remember" class="inputbox" value="yes" alt="Remember Me"> Remember me<br>
                                <input type="hidden" name="option" value="login"> <input type="submit" name="Submit" class="button" value="Login"></td>
                              </tr>
                              <tr>
                                <td>
                                  <a href="index.php?option=com_registration&amp;task=lostPassword">Password Reminder</a>
                                </td>
                              </tr>
                            </table><input type="hidden" name="op2" value="login"> <input type="hidden" name="lang" value="english"> <input type="hidden" name="return" value="/index.php?option=com_content&amp;task=view&amp;id=117&amp;Itemid=54"> <input type="hidden" name="message" value="0">
                          </form>
                        </td>
                      </tr>
                    </table>
                  </div>
                </td>
                <td valign="top" bgcolor="#FAFAFA" width="100%">
                  <div>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tr valign="top" bgcolor="#F1F1F1">
                        <td colspan="3" style="border-top: 3px solid #FFFFFF;">
                          <div>
                            <table cellpadding="0" cellspacing="0" class="moduletable">
                              <tr>
                                <td></td>
                              </tr>
                            </table>
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td></td>
                      </tr>
                      <tr align="left" valign="top">
                        <td colspan="3" style="border-top: 4px solid #FFFFFF; padding: 5px;">
                          <div class="main">
                            <table class="contentpaneopen">
                              <tr>
                                <td class="contentheading" width="100%">Levers are Machines -1</td>
                                <td align="right" width="100%" class="buttonheading">
                                  <a href="javascript:void%20window.open('https://SimpleScience.info//index2.php?option=com_content&amp;task=view&amp;id=117&amp;Itemid=54&amp;pop=1&amp;page=0',%20'win2',%20'status=no,toolbar=no,scrollbars=yes,titlebar=no,menubar=no,resizable=yes,width=640,height=480,directories=no,location=no');" title="Print"><img src="/images/M_images/printButton.png" alt="Print" align="middle" name="image" border="0" id="image"></a>
                                </td>
                                <td align="right" width="100%" class="buttonheading">
                                  <a href="javascript:void%20window.open('https://SimpleScience.info//index2.php?option=com_content&amp;task=emailform&amp;id=117',%20'win2',%20'status=no,toolbar=no,scrollbars=yes,titlebar=no,menubar=no,resizable=yes,width=400,height=250,directories=no,location=no');" title="E-mail"><img src="/images/M_images/emailButton.png" alt="E-mail" align="middle" name="image" border="0" id="image"></a>
                                </td>
                              </tr>
                            </table>
                            <table class="contentpaneopen">
                              <tr>
                                <td width="70%" align="left" valign="top" colspan="2"><span class="small">Written by Upali Salpadoru</span> &nbsp;&nbsp;</td>
                              </tr>
                              <tr>
                                <td valign="top" colspan="2" class="createdate">Saturday, 19 January 2008</td>
                              </tr>
                              <tr>
                                <td valign="top" colspan="2">
                                  <p style="margin: 0cm 0cm 0pt" class="MsoNormal">&nbsp;</p>
                                  <p style="margin: 0cm 0cm 0pt" class="MsoNormal"><font face="Times New Roman" size="3" color="#000000">.A lever is a rod or a plank used for any of the following purposes.</font></p>
                                  <p style="margin: 0cm 0cm 0pt" class="MsoNormal">&nbsp;i.&nbsp; To change the direction of movement.</p>
                                  <p style="margin: 0cm 0cm 0pt" class="MsoNormal">ii.&nbsp; To change the magnitude of the movement.</p>
                                  <p style="margin: 0cm 0cm 0pt" class="MsoNormal">iii. To change the size and direction of a force.&nbsp; .<br></p>
                                  <p style="margin: 0cm 0cm 0pt" class="MsoNormal"><em><font face="Times New Roman" size="3" color="#000000">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &quot;&nbsp;&nbsp; Seesaw up and down,</font></em></p>
                                  <p style="margin: 0cm 0cm 0pt" class="MsoNormal"><em><font face="Times New Roman" size="3" color="#000000">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Baby goes to London</font></em> <font face="Times New Roman" size="3" color="#000000"><em>Town&quot; &nbsp; &nbsp; &nbsp; &nbsp;</em></font></p>
                                  <p style="margin: 0cm 0cm 0pt" class="MsoNormal"><font face="Times New Roman" size="3" color="#000000"></font>is an example of a lever</p>
                                  <p style="margin: 0cm 0cm 0pt" class="MsoNormal"><font face="Times New Roman" size="3" color="#000000"><br></font></p>
                                  <p style="margin: 0cm 0cm 0pt" class="MsoNormal"><img style="width: 240px; height: 224px" src="/images/stories/117/seesaw%202.bmp" alt="" width="240" height="224" align="bottom"></p>
                                  <p style="margin: 0cm 0cm 0pt" class="MsoNormal"><strong>Fig.1. The Blue Babe has lifted the Yellow one How come?</strong></p>
                                  <p style="margin: 0cm 0cm 0pt" class="MsoNormal">&nbsp;</p>
                                  <p>It is generally believed that , a uniform stick supported in the middle will balance when the weights on the two sides are equal. A simple experiment may show that this can be a&nbsp; fallacy.<br>
                                  <img src="/images/stories/117/lev%201.jpg" alt="" width="410" height="250" align="bottom"><br></p>
                                  <p>Fig. 2 Nelly&rsquo;s balancing feat.<br>
                                  <br>
                                  Nelly is trying to balance a 20cm long ruler .&nbsp; A coin is placed at one end 8cm away from the fixed point and two similar coins are placed 4 cm away. Do you think she can balance a heavy weight and a light weight? You can try this out for yourself.<br>
                                  <strong>A demonstration</strong></p>
                                  <p><img src="/images/stories/117/nnl3.jpg" alt="" width="563" height="283" align="bottom"></p>
                                  <p><br>
                                  As the candle wax drops the paper cat and dog will go up and down, but not to London town.<br></p>
                                  <p><strong>Turning Effects of a Force&nbsp;&nbsp; (torque)</strong></p>
                                  <p><br>
                                  We can solve this kind of problems by considering turning effects. The turning ability of a force does not depend merely on the magnitude and the direction of the force. The point where the force is applied also becomes crucial. Greater the distance from the fixed point to the point of force , greater will be the turning ability. Therefore in order to find the turning effect, which is called the MOMENTUM&nbsp; or TORQUE, we consider the product of force and the distance to fixed point. The fixed point is also called the <strong>Pivot</strong> or <strong>fulcrum</strong>.<br>
                                  <br></p>
                                  <blockquote>
                                    <strong>Momentum&nbsp; (Torque)&nbsp; =&nbsp; Force X Distance from Force to Pivot.</strong><br>
                                  </blockquote>
                                  <p><br>
                                  For an object to balance, that is to achieve equilibrium, the sum of the clockwise moments has to become equal to the sum of anti clockwise moments. This is known as the ;Law of Moments or the Principle of Moments.<br></p>
                                  <p><strong>The&nbsp; Law of Moments</strong></p>
                                  <p><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; At Equilibrium,<br></strong></p>
                                  <p><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp; The sum of Clockwise Moments = Sum of Anti clockwise moments</strong>.&nbsp;</p>
                                  <p><img src="/images/stories/117/steer%202.jpg" alt="" width="329" height="272" align="bottom">&nbsp;</p>
                                  <p><strong>Fig.3.&nbsp; Turning forces</strong></p>
                                  <p>Turning is a very common exercise we have to do in everyday life. There are often two parallel forces used either clockwise or anti clockwise. The torque of a couole is equal to the product of force and diameter of the circle.</p>
                                  <p>Torque of a couple =&nbsp; Force x diameter.&nbsp;</p>
                                  <p><strong>&nbsp;Class of Lever</strong></p>
                                  <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <strong>Order one</strong><br>
                                  &nbsp;</p>
                                  <p>&nbsp;<img src="/images/stories/117/lev%202.jpg" alt="" width="432" height="322" align="bottom"><br></p>
                                  <p>Fig. 4&nbsp; A garden fork being used as a Class 1 Lever.&nbsp;</p>
                                  <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <strong>Order two</strong><br></p>
                                  <p>&nbsp;<img src="/images/stories/117/class%202.jpg" alt="" width="272" height="208" align="bottom"></p>
                                  <p>&nbsp;Fig.5&nbsp; In lifting a wheelbarrow, we use it as a Class II lever.</p>
                                  <p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <strong>Order three</strong><br>
                                  &nbsp;</p>
                                  <p>&nbsp;<img src="/images/stories/117/class%203.jpg" alt="" width="423" height="293" align="bottom"><br>
                                  Fig.6&nbsp; Fishing rod is often used as a Class III lever.&nbsp;</p>
                                  <p>&nbsp;</p>
                                  <p><strong>Weight of a Lever</strong><br>
                                  <br>
                                  Sometimes the weight of a lever also can help the lift. It is important to know where the weight of a certain body acts. The point through which the weight acts is called the &lsquo;Centre of Mass&rsquo; or &lsquo;Centre of Gravity&rsquo; and is usually denoted by the letter G. If you have a stick, which is not uniform, G has to be determined by an experiment. Shift&nbsp; the stick on a support until the two sides balance. The balancing point will be the Centre of Gravity.<br></p>
                                  <p><img src="/images/stories/117/g.jpg" alt="" width="432" height="198" align="bottom"><br>
                                  Fig.7 Finding the Centre of Gravity of an un-even stick&nbsp;</p>
                                  <p>&nbsp;</p>
                                  <p><strong>An Experiment</strong><br>
                                  &nbsp;<strong>Aim:&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; To find the weight of a stick using some given weights,</strong><br>
                                  <br>
                                  First we must determine the Centre of Gravity of the stick.&nbsp; Attach a pan to the other end of the balance . You will be able to balance the stick at a point between it&rsquo;s G and the added weight</p>
                                  <p>&nbsp;<img src="/images/stories/117/weight.2.jpg" alt="" width="420" height="299" align="bottom"></p>
                                  <p>Fig.8&nbsp; Given weight balances the stick.&nbsp;</p>
                                  <p>&nbsp;</p>
                                  <p><strong>Formula to be used</strong>;<br>
                                  The sum of ACW moments = the sum of CW moments. <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; &nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span> <span></span>ACW = Anti clockwise <span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span>&nbsp;&nbsp;</span><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span> CW = clockwise<br>
                                  That is&nbsp;&nbsp;&nbsp;&nbsp;</p>
                                  <p class="MsoNormal">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;W1&nbsp; x&nbsp;&nbsp; d1&nbsp;=&nbsp; W2 <span>&nbsp;</span>x d2 <span>&nbsp;</span><span>&nbsp;</span><span>&nbsp;&nbsp;&nbsp;</span></p>
                                  <p class="MsoNormal">&nbsp;Therefore&nbsp;&nbsp; W1&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;= <u></u><span>&nbsp;</span>(W2 x d2) &divide; d1</p>
                                  <p class="MsoNormal"><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span> = (10 x 24)<span>&nbsp;</span> &divide; 16</p>
                                  <p class="MsoNormal">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <u>=&nbsp;&nbsp; <strong>15 N</strong></u></p>
                                  <blockquote>
                                    <blockquote>
                                      <p class="MsoNormal">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</p>
                                      <table border="0" cellspacing="0" cellpadding="0" class="MsoNormalTable" style="margin-left: 19.6pt; border-collapse: collapse">
                                        <tbody>
                                          <tr>
                                            <td width="143" valign="top" style="border: 1pt solid windowtext; padding: 0cm 5.4pt; background-color: #ffff99; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 50%; -moz-background-size: auto auto; width: 107pt; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                              <p class="MsoNormal"><strong><span>&nbsp;&nbsp;</span> Wt. given&nbsp;&nbsp;&nbsp;&nbsp; W 2</strong></p>
                                            </td>
                                            <td width="150" valign="top" style="border-style: solid solid solid none; border-color: windowtext windowtext windowtext -moz-use-text-color; border-width: 1pt 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #ffff99; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 50%; -moz-background-size: auto auto; width: 112.7pt; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                              <p class="MsoNormal"><strong><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span> D 2</strong></p>
                                            </td>
                                            <td width="132" valign="top" style="border-style: solid solid solid none; border-color: windowtext windowtext windowtext -moz-use-text-color; border-width: 1pt 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #ffff99; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 50%; -moz-background-size: auto auto; width: 99.25pt; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                              <p class="MsoNormal"><strong><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span> D 1</strong></p>
                                            </td>
                                            <td width="170" valign="top" style="border-style: solid solid solid none; border-color: windowtext windowtext windowtext -moz-use-text-color; border-width: 1pt 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #ffff99; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 50%; -moz-background-size: auto auto; width: 127.55pt; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                              <p class="MsoNormal"><strong>Weight of stick&nbsp; W 1</strong></p>
                                            </td>
                                          </tr>
                                          <tr>
                                            <td width="143" valign="top" style="border-style: none solid solid; border-color: -moz-use-text-color windowtext windowtext; border-width: medium 1pt 1pt; padding: 0cm 5.4pt; width: 107pt">
                                              <p class="MsoNormal">10 N</p>
                                            </td>
                                            <td width="150" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; width: 112.7pt">
                                              <p class="MsoNormal">24cm</p>
                                            </td>
                                            <td width="132" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; width: 99.25pt">
                                              <p class="MsoNormal">16cm</p>
                                            </td>
                                            <td width="170" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; width: 127.55pt">
                                              <p class="MsoNormal">(10&nbsp; x&nbsp; 24) &divide; 16</p>
                                              <p class="MsoNormal">=&nbsp;&nbsp; 15 N</p>
                                            </td>
                                          </tr>
                                          <tr>
                                            <td width="143" valign="top" style="border-style: none solid solid; border-color: -moz-use-text-color windowtext windowtext; border-width: medium 1pt 1pt; padding: 0cm 5.4pt; width: 107pt">
                                              <p class="MsoNormal">&nbsp;</p>
                                            </td>
                                            <td width="150" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; width: 112.7pt">
                                              <p class="MsoNormal">&nbsp;</p>
                                            </td>
                                            <td width="132" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; width: 99.25pt">
                                              <p class="MsoNormal">&nbsp;</p>
                                            </td>
                                            <td width="170" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; width: 127.55pt">
                                              <p class="MsoNormal">&nbsp;</p>
                                            </td>
                                          </tr>
                                        </tbody>
                                      </table>
                                      <p>This is an acrobatic show. The weights of Nelly and the lever are pushing the heads of the two boys. Some weights and distances are given. You have to find the Reaction forces the boys have to give&nbsp; to maintain the equilibrium.<br>
                                      <strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</strong></p>
                                      <p><strong>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Data Sheet</strong></p>
                                      <p class="MsoNormal">Total length of the board&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;<span>&nbsp;</span> <span>&nbsp;</span>=&nbsp; 3m<br>
                                      Weight of the board.&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp; = 120N<br>
                                      Weight of Nelly&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; &nbsp; =&nbsp; 550N<br>
                                      Distance from Ali to Nelly&nbsp;&nbsp; &nbsp;&nbsp; = 0.75m<br>
                                      &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; G is in the middle.</p>
                                    </blockquote>
                                  </blockquote>
                                  <p>&nbsp;<img src="/images/stories/117/acrobatics.jpg" alt="" width="379" height="291" align="bottom"></p>
                                  <p>Fig. 9. Acrobatics.<br></p>
                                  <p>In this case the Fixed point or the Pivot is not given&nbsp; We may assume Ali or Bunty as the pivot and work out the CW and ACW moments.<br>
                                  Let A be the reaction force of Ali and B be the reaction force of Bunty.<br>
                                  The sum of&nbsp;&nbsp; ACW moments&nbsp;&nbsp; =&nbsp;&nbsp; The sum of CW moments.<br>
                                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; &nbsp; &nbsp; B x 3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; =&nbsp;&nbsp; (550x 0.75) +&nbsp;&nbsp; (120 x&nbsp; 1.5)&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp;<br>
                                  <br>
                                  Therefore B&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; &nbsp; =&nbsp;&nbsp;&nbsp;&nbsp; 592.5&nbsp;&nbsp;&nbsp; &divide;&nbsp;&nbsp; 3<br>
                                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; <u>=&nbsp;&nbsp;&nbsp;&nbsp; <strong>197.5</strong><br></u><br>
                                  <br>
                                  Similarly if you take B as the pivot we can get the Reaction force of Ali.<br>
                                  &nbsp;CW moments =&nbsp; ACW moments<br>
                                  &nbsp;&nbsp;&nbsp;&nbsp; A x 3&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; =&nbsp; (550 x 2.25 )&nbsp; +&nbsp;&nbsp; (120 x 1.5)<br>
                                  <br>
                                  &nbsp;&nbsp;&nbsp; A&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; =&nbsp;&nbsp; 1417.5 &divide;&nbsp; 3<br>
                                  &nbsp;&nbsp;&nbsp;&nbsp; &nbsp; &nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; =&nbsp;&nbsp;&nbsp; <u><strong>472.5</strong></u> &nbsp;<br></p>
                                  <p><strong>Verification</strong><br>
                                  If your answers are correct R at A and R at B must be equal to the Weight of Nelly + Weight of Board.<br>
                                  Sum of&nbsp;&nbsp; Weights 120 + 550&nbsp; &hellip;&hellip;&hellip;.. =&nbsp; 670<br>
                                  Sum of R forces&nbsp; 197.5 + 472 .5&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; =&nbsp; 670<br>
                                  &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; This shows that our answers are correct.<br>
                                  <br></p>
                                  <p>&nbsp;</p><br>
                                </td>
                              </tr>
                              <tr>
                                <td colspan="2" align="left" class="modifydate">Last Updated ( Saturday, 17 April 2010 )</td>
                              </tr>
                            </table>
                          </div>
                        </td>
                      </tr>
                      <tr bgcolor="#F1F1F1">
                        <td colspan="3" valign="top" style="border-top: 3px solid #FFFFFF;"></td>
                      </tr>
                    </table>
                  </div>
                </td>
                <td valign="top" style="background-repeat: repeat-y;" background="https://SimpleScience.info//templates/247portal-blue/images/rb.gif"></td>
                <td valign="top" style="padding-right: 8px; background-repeat: repeat-y;" background="https://SimpleScience.info//templates/247portal-blue/images/shadowr.jpg">&nbsp;</td>
              </tr>
            </table>
            <table width="100%" border="0" cellpadding="0" cellspacing="0" background="https://SimpleScience.info//templates/247portal-blue/images/center2.jpg">
              <tr>
                <td width="26"><img src="/templates/247portal-blue/images/left2.jpg"></td>
                <td>
                  <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                      <td width="30" align="left">
                        <a href="/index.php?option=com_content&task=view&id=117&Itemid=54#up"><img src="/templates/247portal-blue/images/ltop.jpg" alt="Top!" border="0"></a>
                      </td>
                      <td align="center">
                        <div class="footer">
                          <div align="center">
                            <br>
                            Mambo is Free Software released under the GNU/GPL License. Web Site Supported by: Charlies Research
                          </div>Design by Mamboteam.com!
                        </div>
                      </td>
                      <td width="30" align="right">
                        <a href="/index.php?option=com_content&task=view&id=117&Itemid=54#up"><img src="/templates/247portal-blue/images/rtop.jpg" alt="Top!" border="0"></a>
                      </td>
                    </tr>
                  </table>
                </td>
                <td width="26"><img src="/templates/247portal-blue/images/right2.jpg"></td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
  </div>
  <script type="text/javascript">
  function createCookie(name,value,days) {
  if (days) {
  var date = new Date();
  date.setTime(date.getTime()+(days*24*60*60*1000));
  var expires = "; expires="+date.toGMTString();
  }
  else var expires = "";
  document.cookie = name+"="+value+expires+"; path=/";
  }
  function readCookie(name) {
  var nameEQ = name + "=";
  var ca = document.cookie.split(';');
  for(var i=0;i < ca.length;i++) {
  var c = ca[i];
  while (c.charAt(0)==' ') c = c.substring(1,c.length);
  if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
  }
  return null;
  }
  function eraseCookie(name) {
  alert('hello world!');
  createCookie(name,"",-1);
  }
  function popUpAd(){
  // alert('hello world!');
  // alert(readCookie("visited"));
  if (readCookie("visited")==2)
  {
  //  alert("yes");
  }
  else
  {
  //  alert("no");
  if (readCookie("visited")==1)
  {
  createCookie("visited",2,2);
  }else{
  createCookie("visited",1,2);

  }
  window.open("http://www.charliesindex.com","mywindow"); 
  }
  }

  </script>
</body>
</html>
