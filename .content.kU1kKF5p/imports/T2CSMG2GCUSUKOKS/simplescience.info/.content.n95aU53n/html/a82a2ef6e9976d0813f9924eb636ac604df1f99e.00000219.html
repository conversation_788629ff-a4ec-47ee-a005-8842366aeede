<?xml version="1.0" encoding="iso-8859-1"?>
<!DOCTYPE html>
<html>
<head>
  <title>SimpleScience.info - Fear of Cholesterol</title>
  <meta name="title" content="Fear of Cholesterol">
  <meta name="description" content="physics chemistry biology">
  <meta name="keywords" content="physics chemistry biology">
  <link rel="shortcut icon" href="/images/favicon.ico">
  <meta http-equiv="Content-Type" content="text/html;&gt;charset=iso-8859-1">
  <link rel="stylesheet" href="/templates/247portal-blue/css/template_css.css" type="text/css">
  <link rel="shortcut icon" href="/templates/247portal-blue/favicon.ico">
  <link rel="alternate" title="SimpleScience.info" href="/index2.php?option=com_rss&no_html=1" type="application/rss+xml">
  <script language="JavaScript" type="text/javascript">
    <!--
    function MM_reloadPage(init) {  //reloads the window if Nav4 resized
      if (init==true) with (navigator) {if ((appName=="Netscape")&&(parseInt(appVersion)==4)) {
        document.MM_pgW=innerWidth; document.MM_pgH=innerHeight; onresize=MM_reloadPage; }}
      else if (innerWidth!=document.MM_pgW || innerHeight!=document.MM_pgH) location.reload();
    }
    MM_reloadPage(true);
    //-->
  </script>
  <style type="text/css">
  <!--
  .Stil1 {
        font-size: xx-small;
        color: #FFFFFF;
  }
  -->
  </style>
</head>
<body onload="popUpAd()">
  <div>
    <a name="up" id="up"></a>
    <table width="870" height="20" border="0" align="center" cellpadding="0" cellspacing="0">
      <tr>
        <td align="center" class="mt">www.mamboteam.com</td>
      </tr>
    </table>
    <table width="870" border="0" align="center" cellpadding="0" cellspacing="0">
      <tr>
        <td><img src="/templates/247portal-blue/images/space.gif" width="770" height="1"></td>
      </tr>
      <tr>
        <td>
          <div class="background">
            <table width="100%" border="0" cellpadding="0" cellspacing="0" background="https://SimpleScience.info//templates/247portal-blue/images/center.jpg">
              <tr>
                <td width="26"><img src="/templates/247portal-blue/images/left.jpg" width="26"></td>
                <td class="title">
                  <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                      <td height="100" colspan="2" class="title">
                        <table width="100%" border="0" cellpadding="0" cellspacing="0">
                          <tr>
                            <td width="100%" style="padding-left:10px;padding-top:14px;">
                              <a href="/" title="SimpleScience.info">SimpleScience.info</a>
                            </td>
                            <td style="padding-left:10px;padding-top:14px;"><img style='border:3px solid white;' src='http://www.LiveRanks.com/images/LiveRanks_Certified_468_60.png'></td>
                          </tr>
                        </table>
                      </td>
                    </tr>
                    <tr>
                      <td width="400" height="29">
                        <div id="search">
                          <form action="index.php" method="post">
                            <div align="left" class="search">
                              <input alt="search" class="inputbox" type="text" name="searchword" size="20" value="search..." onblur="if(this.value=='') this.value='search...';" onfocus="if(this.value=='search...') this.value='';">
                            </div><input type="hidden" name="option" value="search">
                          </form>
                        </div>
                      </td>
                      <td width="100%" height="29" valign="bottom" class="mainlevel-nav"></td>
                    </tr>
                  </table>
                </td>
                <td width="26"><img src="/templates/247portal-blue/images/right.jpg" width="26"></td>
              </tr>
            </table>
            <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
              <tr>
                <td width="11" height="25" background="https://SimpleScience.info//templates/247portal-blue/images/shadowl.jpg">
                  <div></div>
                </td>
                <td height="25" bgcolor="#F1F1F1" style="border-bottom: 1px solid #999999; border-top: 5px solid #FFFFFF;"><span class="pathway"><a href="index.php" class="pathway">Home</a> <img src="/images/M_images/arrow.png" alt="arrow"> <a href="index.php?option=com_content&amp;task=section&amp;id=6&amp;Itemid=54" class="pathway">Content Index</a> <img src="/images/M_images/arrow.png" alt="arrow"> <a href="index.php?option=com_content&amp;task=category&amp;sectionid=6&amp;id=23&amp;Itemid=54" class="pathway">General Knowledge</a> <img src="/images/M_images/arrow.png" alt="arrow"> Fear of Cholesterol</span></td>
                <td height="25" align="right" bgcolor="#F1F1F1" style="border-bottom: 1px solid #999999; border-top: 5px solid #FFFFFF;">
                  <div class="date">
                    Monday, 02 April 2012
                  </div>
                </td>
                <td width="11" height="25" align="right" background="https://SimpleScience.info//templates/247portal-blue/images/shadowr.jpg">&nbsp;</td>
              </tr>
            </table>
            <table width="100%" border="0" align="center" cellpadding="0" cellspacing="0">
              <tr>
                <td valign="top" style="padding-left:8px; background-repeat: repeat-y;" background="https://SimpleScience.info//templates/247portal-blue/images/shadowl.jpg">&nbsp;</td>
                <td valign="top" style="background-repeat: repeat-y;" background="https://SimpleScience.info//templates/247portal-blue/images/lb.gif">
                  <div class="leftrow">
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <td>
                          <table width="100%" border="0" cellpadding="0" cellspacing="0">
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_frontpage&amp;Itemid=1" class="mainlevel">Home</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=section&amp;id=6&amp;Itemid=54" class="mainlevel" id="active_menu">Content Index</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=blogcategory&amp;id=24&amp;Itemid=48" class="mainlevel">Simple Science</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=blogcategory&amp;id=22&amp;Itemid=63" class="mainlevel">General Science</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=blogcategory&amp;id=23&amp;Itemid=49" class="mainlevel">General Knowledge</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=blogcategory&amp;id=25&amp;Itemid=50" class="mainlevel">Biographies</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=blogcategory&amp;id=3&amp;Itemid=61" class="mainlevel">Site Services</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_content&amp;task=view&amp;id=49&amp;Itemid=55" class="mainlevel">About us</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="index.php?option=com_contact&amp;catid=12&amp;Itemid=65" class="mainlevel">Contact us</a>
                              </td>
                            </tr>
                            <tr align="left">
                              <td>
                                <a href="#" onclick="javascript: window.open('https://simplescience.info/forum/index.php?action=register&amp;Itemid=67', '', 'toolbar=no,location=no,status=no,menubar=no,scrollbars=yes,resizable=yes,width=780,height=550'); return false" class="mainlevel">Register on Forum</a>
                              </td>
                            </tr>
                          </table>
                        </td>
                      </tr>
                    </table>
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <td>
                          <div style="float:center;width:120px;height:600px;margin:auto;padding:auto;">
                            <script type="text/javascript">
                            <!--
                            google_ad_client = "pub-2867192467939979";
                            google_alternate_ad_url = "http://www.alternateurl.com/show?memid=944&size=120x600"; 
                            google_alternate_color = "FFFFFF"; 
                            google_ad_width = 120; 
                            google_ad_height = 600; 
                            google_ad_format = "120x600_as"; 
                            google_ad_type = "text_image"; 
                            google_ad_channel = "1234399007"; 
                            google_color_border = ["F1F1F1","F1F1F1","F1F1F1","F1F1F1"]; 
                            google_color_bg = ["F1F1F1","F1F1F1","F1F1F1","F1F1F1"]; 
                            google_color_link = ["0033FF","0033FF","0033FF","0033FF"]; 
                            google_color_url = ["008000","008000","008000","008000"]; 
                            google_color_text = ["333333","333333","333333","333333"]; 
                            //--> 
                            </script> 
                            <script type="text/javascript" src="http://pagead2.googlesyndication.com/pagead/show_ads.js"></script>
                          </div>
                        </td>
                      </tr>
                    </table>
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <th valign="top">Search here</th>
                      </tr>
                      <tr>
                        <td>
                          <form method="get" action="http://www.google.com/custom" target="_top">
                            <table border="0" cellspacing="0" cellpadding="0" bgcolor="#F1F1F1">
                              <tr>
                                <td nowrap="nowrap" valign="top" align="left" height="32"><img src="http://www.google.com/logos/Logo_25wht.gif" border="0" alt="Google" align="middle"><br>
                                <input type="text" name="q" size="20" maxlength="255" value=""></td>
                              </tr>
                              <tr>
                                <td valign="top" align="left"><input type="submit" name="sa" value="Search"> <input type="hidden" name="client" value="pub-2867192467939979"> <input type="hidden" name="channel" value="1234399007"> <input type="hidden" name="ie" value="ISO-8859-1"> <input type="hidden" name="oe" value="ISO-8859-1"> <input type="hidden" name="cof" value="GALT:#008000;GL:1;DIV:#FFFFFF;VLC:0000CC;AH:center;BGC:F1F1F1;LBGC:FFFFFF;ALC:0000CC;LC:0000CC;T:333333;GFNT:7777CC;GIMP:7777CC;LH:41;LW:120;L:http://www.google.com/logos/Logo_40wht.gif;S:http://mambo.medspan.info;LP:1;FORID:1;"> <input type="hidden" name="hl" value="en"></td>
                              </tr>
                            </table>
                          </form>
                        </td>
                      </tr>
                    </table>
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <th valign="top">Popular Articles</th>
                      </tr>
                      <tr>
                        <td>
                          <ul class="mostread">
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=114&amp;Itemid=54" class="mostread">Kingdom of Bacteria</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=117&amp;Itemid=54" class="mostread">Levers are Machines -1</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=80&amp;Itemid=54" class="mostread">Breathing & Respiration</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=167&amp;Itemid=54" class="mostread">Karl Benz</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=103&amp;Itemid=54" class="mostread">Friction is a Force</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=76&amp;Itemid=54" class="mostread">Introducing forces</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=97&amp;Itemid=54" class="mostread">The Periodic Table. 1-18.</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=173&amp;Itemid=54" class="mostread">Burning is a chemical change</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=48&amp;Itemid=54" class="mostread">Laureate Marie Curie</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=175&amp;Itemid=54" class="mostread">Gene- The basic unit of heredity</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=158&amp;Itemid=54" class="mostread">2. Ch.cal.Relative Molecular Mass</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=44&amp;Itemid=54" class="mostread">Laureate R Faynman</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=116&amp;Itemid=54" class="mostread">Pulleys are Machines</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=51&amp;Itemid=54" class="mostread">Brilliant Halo from a CD</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=77&amp;Itemid=54" class="mostread">Ionic compounds</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=57&amp;Itemid=54" class="mostread">Laureate Rontgen</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=75&amp;Itemid=54" class="mostread">Heart & Circulation</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=45&amp;Itemid=54" class="mostread">Teasing a Friend</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=115&amp;Itemid=54" class="mostread">Force of Gravity</a>
                            </li>
                            <li class="latestnews">
                              <a href="index.php?option=com_content&amp;task=view&amp;id=91&amp;Itemid=54" class="mostread">Apnea- A Sleep Disorder.</a>
                            </li>
                          </ul>
                        </td>
                      </tr>
                    </table>
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <th valign="top">Make a Payment</th>
                      </tr>
                      <tr>
                        <td>
                          <script language="javascript" type="text/javascript">
                          function iFrameHeight() {
                          var h = 0;
                          if ( !document.all ) {
                          h = document.getElementById('blockrandom').contentDocument.height;
                          document.getElementById('blockrandom').style.height = h + 60 + 'px';
                          } else if( document.all ) {
                          h = document.frames('blockrandom').document.body.scrollHeight;
                          document.all.blockrandom.style.height = h + 20 + 'px';
                          }
                          }
                          </script> <iframe id="blockrandom" src="/new_code/donate.html" width="100%" height="80" scrolling="no" align="top" frameborder="0" class="wrapper" name="blockrandom"></iframe>
                        </td>
                      </tr>
                    </table>
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <th valign="top">Statistics</th>
                      </tr>
                      <tr>
                        <td><strong>Visitors:</strong> 115977</td>
                      </tr>
                    </table>
                    <table cellpadding="0" cellspacing="0" class="moduletable">
                      <tr>
                        <th valign="top">Login Form</th>
                      </tr>
                      <tr>
                        <td>
                          <form action="index.php" method="post" name="login" id="login">
                            <table width="100%" border="0" cellspacing="0" cellpadding="0" align="center">
                              <tr>
                                <td>Username<br>
                                <input name="username" type="text" class="inputbox" alt="username" size="10"><br>
                                Password<br>
                                <input type="password" name="passwd" class="inputbox" size="10" alt="password"><br>
                                <input type="checkbox" name="remember" class="inputbox" value="yes" alt="Remember Me"> Remember me<br>
                                <input type="hidden" name="option" value="login"> <input type="submit" name="Submit" class="button" value="Login"></td>
                              </tr>
                              <tr>
                                <td>
                                  <a href="index.php?option=com_registration&amp;task=lostPassword">Password Reminder</a>
                                </td>
                              </tr>
                            </table><input type="hidden" name="op2" value="login"> <input type="hidden" name="lang" value="english"> <input type="hidden" name="return" value="/index.php?option=com_content&amp;task=view&amp;id=190&amp;Itemid=54"> <input type="hidden" name="message" value="0">
                          </form>
                        </td>
                      </tr>
                    </table>
                  </div>
                </td>
                <td valign="top" bgcolor="#FAFAFA" width="100%">
                  <div>
                    <table width="100%" border="0" cellspacing="0" cellpadding="0">
                      <tr valign="top" bgcolor="#F1F1F1">
                        <td colspan="3" style="border-top: 3px solid #FFFFFF;">
                          <div>
                            <table cellpadding="0" cellspacing="0" class="moduletable">
                              <tr>
                                <td></td>
                              </tr>
                            </table>
                          </div>
                        </td>
                      </tr>
                      <tr>
                        <td></td>
                      </tr>
                      <tr align="left" valign="top">
                        <td colspan="3" style="border-top: 4px solid #FFFFFF; padding: 5px;">
                          <div class="main">
                            <table class="contentpaneopen">
                              <tr>
                                <td class="contentheading" width="100%">Fear of Cholesterol</td>
                                <td align="right" width="100%" class="buttonheading">
                                  <a href="javascript:void%20window.open('https://SimpleScience.info//index2.php?option=com_content&amp;task=view&amp;id=190&amp;Itemid=54&amp;pop=1&amp;page=0',%20'win2',%20'status=no,toolbar=no,scrollbars=yes,titlebar=no,menubar=no,resizable=yes,width=640,height=480,directories=no,location=no');" title="Print"><img src="/images/M_images/printButton.png" alt="Print" align="middle" name="image" border="0" id="image"></a>
                                </td>
                                <td align="right" width="100%" class="buttonheading">
                                  <a href="javascript:void%20window.open('https://SimpleScience.info//index2.php?option=com_content&amp;task=emailform&amp;id=190',%20'win2',%20'status=no,toolbar=no,scrollbars=yes,titlebar=no,menubar=no,resizable=yes,width=400,height=250,directories=no,location=no');" title="E-mail"><img src="/images/M_images/emailButton.png" alt="E-mail" align="middle" name="image" border="0" id="image"></a>
                                </td>
                              </tr>
                            </table>
                            <table class="contentpaneopen">
                              <tr>
                                <td width="70%" align="left" valign="top" colspan="2"><span class="small">Written by Upali Salpadoru</span> &nbsp;&nbsp;</td>
                              </tr>
                              <tr>
                                <td valign="top" colspan="2" class="createdate">Tuesday, 13 October 2009</td>
                              </tr>
                              <tr>
                                <td valign="top" colspan="2">
                                  <p>&nbsp;A Research group at the Max Delbruck centre Molecular Medicine, Berlin, got the shock of their lives when they added cholesterol to some&nbsp; brain cells&nbsp; in a petri dish. Nerve cells have tentacle like projections called dendrites. The longest is called the axon. The endings of these join to other nerve cells through highly specialised contact points called synapses. According to them, these synapses may not only passing messages but probably responsible for the learning process and the development of the memory.</p>
                                  <p><img src="/images/stories/189Neu/neuron.jpg" alt="" width="322" height="318" align="baseline">&nbsp;</p>
                                  <p>Fig.1.&nbsp; The cells shown in pink, dark brown and blue are neurons.&nbsp; .<br></p>
                                  <p>&nbsp;If you are an adult, living in the city, it is quite likely, you too are a victim of this popular disease; cholesterophobia. While this has brought anxiety to most of the middle and upper class gentle folk, multinational pharmaceutical ventures, receive a roaring income, year after year. &ldquo;In 2004 Pfizer netted $10.9 billion in sales from their cholesterol-lowering drug atorvastatin (popularly known as Lipitor).<br>
                                  &nbsp;<br>
                                  According to recent biomedical research, cholesterol present in our body is &lsquo;the mother of all beneficial fat molecules&rsquo;. But we are being made to believe, via the sales reps, who even influence the members of the medical profession, that it is the&nbsp; &lsquo; father of all evil&rsquo;.&nbsp; No other compound in the human system has&nbsp; ever been vilified, as much as this simple waxy, sterol; named&nbsp; cholesterol &hellip;C27H45OH.<br>
                                  <br>
                                  If you have had a fasting lipid profile blood tests, the values of HDL and LDL would have given you&nbsp; some anxious moments.&nbsp; These are lipoproteins; compounds containing lipids and proteins linked together. This form is necessary for cholesterol to gain entry into the blood. Eighty percent of the cholesterol you need is made inside your body, mainly by the liver and the intestinal cells. As the cholesterol cannot pass the blood brain barrier it is also synthesized in the brain.&nbsp; The two important forms, it runs in the blood are ,&nbsp; Low Density Lipoproteins&nbsp; LDL, believed to be&nbsp; the black sheep and&nbsp; High Density Lipoproteins, the saviours.&nbsp; No animal can survive if the cholesterol value is lowered below a critical amount.<br>
                                  <br>
                                  Most of the cholesterol in our body is locked up in the cell membranes. It is common knowledge that we are made of trillions of tiny&nbsp; living cells. All these precious cells&nbsp;&nbsp; are covered with a highly specialised double layered membrane. This membrane while keeping the cell contents safely secured inside, also controls the trafficking of various molecules in and out of the cell. Cholesterol is an essential ingredient for the proper functioning of this process. As the presence of this lipid chemical is so crucial almost all cells have acquired the ability to synthesize it.&nbsp;&nbsp;&nbsp;<br>
                                  The functional cells in our brains are the neurones (nerve cells).&nbsp; Nobody knew the exact function of the lesser known Glial cells, which exceeds the neurons by about ten times. It was known&nbsp; that they made cholesterol to insulate the communication network formed by the nerves.&nbsp;&nbsp; In 2001 a group of researchers at the Delbruck centre in Berlin, &lsquo;added plain cholesterol to nerve cells in a laboratory dish, the nerve cells began to form synapses&nbsp; like crazy&rsquo;. Synapses are junctions through which the nerve cells communicate with each other.<br>
                                  <br>
                                  Fig.&nbsp; A nerve cell showing synapses.<br>
                                  Cholesterol is also an essential ingredient for the synthesis of hormones such as cortisol, the adrenal hormone and the sex hormones testosterone. and oestrogen. It is also essential for the synthesis of the fat soluble vitamins such as&nbsp; A,&nbsp; D, and K,&nbsp; It&nbsp; also plays an important role in the digestion of food by inducing the production and secretion of bile. But the excess of cholesterol is suspected of forming gallstones. Stones inside the gall bladder.<br>
                                  For a normal person having a proper functioning liver and a heart, need not unnecessarily worry about cholesterol.<br></p>
                                  <p class="MsoNormal"><span style="font-size: 11pt">The desirable levels for a normal<span>&nbsp;</span> adult are as follows;</span></p>
                                  <p class="MsoNormal"><span style="font-size: 11pt">mg/L = <em>mili grams per Litre</em>,<span>&nbsp;&nbsp;&nbsp;&nbsp;</span> m mol/ L<span>&nbsp;</span> = <em>mili moles/Liter</em></span></p>
                                  <table border="1" cellspacing="0" cellpadding="0" class="MsoTableGrid" style="border: medium none; border-collapse: collapse">
                                    <tbody>
                                      <tr>
                                        <td width="222" valign="top" style="border: 1pt solid windowtext; padding: 0cm 5.4pt; width: 133pt">
                                          <p style="text-align: center" class="MsoNormal" align="center"><strong><span style="font-size: 11pt">Lipid</span></strong></p>
                                        </td>
                                        <td width="236" valign="top" style="border-style: solid solid solid none; border-color: windowtext windowtext windowtext -moz-use-text-color; border-width: 1pt 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #99ccff; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 0%; -moz-background-size: auto auto; width: 5cm; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                          <p style="text-align: center" class="MsoNormal" align="center"><strong><span style="font-size: 11pt">Desirable</span></strong></p>
                                        </td>
                                        <td width="224" valign="top" style="border-style: solid solid solid none; border-color: windowtext windowtext windowtext -moz-use-text-color; border-width: 1pt 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #ff700f; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 0%; -moz-background-size: auto auto; width: 134.65pt; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                          <p style="text-align: center" class="MsoNormal" align="center"><strong><span style="font-size: 11pt">Risky</span></strong></p>
                                        </td>
                                      </tr>
                                      <tr>
                                        <td width="222" valign="top" style="border-style: none solid solid; border-color: -moz-use-text-color windowtext windowtext; border-width: medium 1pt 1pt; padding: 0cm 5.4pt; width: 133pt">
                                          <p class="MsoNormal"><span style="font-size: 11pt">Total blood cholesterol</span></p>
                                        </td>
                                        <td width="236" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #99ccff; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 0%; -moz-background-size: auto auto; width: 5cm; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                          <p class="MsoNormal"><span style="font-size: 11pt">170 to 210<span>&nbsp;</span> mg / dL</span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt">or</span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt">4<span>&nbsp;</span> m mol/L</span></p>
                                        </td>
                                        <td width="224" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #ff700f; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 0%; -moz-background-size: auto auto; width: 134.65pt; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                          <p class="MsoNormal"><span style="font-size: 11pt">Above<span>&nbsp;</span> 240mg/dL</span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt">or</span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt">Above<span>&nbsp;</span> 6.2 m mol/L</span></p>
                                        </td>
                                      </tr>
                                      <tr>
                                        <td width="222" valign="top" style="border-style: none solid solid; border-color: -moz-use-text-color windowtext windowtext; border-width: medium 1pt 1pt; padding: 0cm 5.4pt; width: 133pt">
                                          <p class="MsoNormal"><span style="font-size: 11pt">LDL Bad cholesterol.</span></p>
                                        </td>
                                        <td width="236" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #99ccff; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 0%; -moz-background-size: auto auto; width: 5cm; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                          <p class="MsoNormal"><span style="font-size: 11pt">60 to140 mg/ dL</span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt">Or<span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt"><span>&nbsp;</span>2.5 m mol/L</span></p>
                                        </td>
                                        <td width="224" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #ff700f; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 0%; -moz-background-size: auto auto; width: 134.65pt; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                          <p class="MsoNormal"><span style="font-size: 11pt">Above 140mg/L</span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt">Or</span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt">Above 4.1m mol/L</span></p>
                                        </td>
                                      </tr>
                                      <tr>
                                        <td width="222" valign="top" style="border-style: none solid solid; border-color: -moz-use-text-color windowtext windowtext; border-width: medium 1pt 1pt; padding: 0cm 5.4pt; width: 133pt">
                                          <p class="MsoNormal"><span style="font-size: 11pt">HDL<span>&nbsp;</span> Good cholesterol.</span></p>
                                        </td>
                                        <td width="236" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #99ccff; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 0%; -moz-background-size: auto auto; width: 5cm; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                          <p class="MsoNormal"><span style="font-size: 11pt">Above 50 <span>&nbsp;</span>mg/dL</span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt">or</span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt">More than 1 mmol/L</span></p>
                                        </td>
                                        <td width="224" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #ff700f; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 0%; -moz-background-size: auto auto; width: 134.65pt; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                          <p class="MsoNormal"><span style="font-size: 11pt">Below 40 mg/L</span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt">Or</span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt">Below 1.5 m mol/L</span></p>
                                        </td>
                                      </tr>
                                      <tr>
                                        <td width="222" valign="top" style="border-style: none solid solid; border-color: -moz-use-text-color windowtext windowtext; border-width: medium 1pt 1pt; padding: 0cm 5.4pt; width: 133pt">
                                          <p class="MsoNormal"><span style="font-size: 11pt">Cholesterol/HDL</span></p>
                                          <p class="MsoNormal"><strong><span style="font-size: 11pt"><span>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span> Cardiac risk ratio</span></strong></p>
                                        </td>
                                        <td width="236" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #99ccff; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 0%; -moz-background-size: auto auto; width: 5cm; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                          <p class="MsoNormal"><span style="font-size: 11pt">4.5</span></p>
                                        </td>
                                        <td width="224" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #ff700f; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 0%; -moz-background-size: auto auto; width: 134.65pt; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                          <p class="MsoNormal"><span style="font-size: 11pt">More than 7</span></p>
                                        </td>
                                      </tr>
                                      <tr>
                                        <td width="222" valign="top" style="border-style: none solid solid; border-color: -moz-use-text-color windowtext windowtext; border-width: medium 1pt 1pt; padding: 0cm 5.4pt; width: 133pt">
                                          <p class="MsoNormal"><span style="font-size: 11pt">Triglyceride</span></p>
                                        </td>
                                        <td width="236" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #99ccff; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 0%; -moz-background-size: auto auto; width: 5cm; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                          <p class="MsoNormal"><span style="font-size: 11pt">40 to 150 mg/L</span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt">1.7 m mol/L</span></p>
                                        </td>
                                        <td width="224" valign="top" style="border-style: none solid solid none; border-color: -moz-use-text-color windowtext windowtext -moz-use-text-color; border-width: medium 1pt 1pt medium; padding: 0cm 5.4pt; background-color: #ff700f; background-image: none; background-repeat: repeat; background-attachment: scroll; background-position: 0% 0%; -moz-background-size: auto auto; width: 134.65pt; -moz-background-clip: -moz-initial; -moz-background-origin: -moz-initial; -moz-background-inline-policy: -moz-initial">
                                          <p class="MsoNormal"><span style="font-size: 11pt">Above n150 mg/L</span></p>
                                          <p class="MsoNormal"><span style="font-size: 11pt">Above 5.0 m mol/L</span></p>
                                        </td>
                                      </tr>
                                    </tbody>
                                  </table>
                                  <p style="margin-left: 36pt; text-indent: -18pt" class="MsoNormal"><span style="font-size: 11pt; font-family: Wingdings"><span>v<span style="font: 7pt &quot;Times New Roman&quot;">&nbsp;&nbsp;&nbsp;&nbsp;</span></span></span> <span><em><span style="font-size: 11pt">Please note that these values are just guide lines and not universally accepted</span></em></span><span style="font-size: 11pt">.</span></p><br>
                                  <br>
                                  <br>
                                  The greatest risk of&nbsp; high LDL level is the formation of plaque in the narrow arteries, a condition known as atherosclerosis. This can cause angina and stroke.&nbsp; Even here cholesterol is not the primary culprit. According to some experts (http;/tuberose.com) it is an amino acid called homocysteine which initiates plaque build up.&nbsp;&nbsp; This substance scratches the wall of the blood vessels preparing a building site for the cholesterol, calcium and other junk to build up a mound blocking the artery. Deficiencies of the vitamins B9, B6, or B12 can lead to high homocysteine levels. Other causes for the disease could be excessive triglycerides, high blood pressure, obesity, smoking and lack of exercise. We wonder why homocysteine levels are not tested in the blood as yet.<br>
                                  <br>
                                  It will be extremely beneficial for your health as well as the pocket if you can avoid saturated fats in meat, milk and egg yolk and trans fat in margarines and consume , fruits,&nbsp; nuts, fish and vegetables supplemented with soy products and veggie oils. This can make favourable adjustments in&nbsp; your cholesterol, homocysteine and triglyceride levels.<br>
                                  <br>
                                  Cholesterol is such an important substance to our system that when it does not come from the food the liver&nbsp; increases the synthesis to maintain the optimum level. So, when the risk factors are high in the in a patient, the doctors are compelled to&nbsp; prescribe statins&nbsp; to lower the LDL levels in spite of their side effects. Yet,&nbsp; as cholesterol is essential for the development of an embryo, pregnant mothers should refrain from taking&nbsp; cholesterol lowering drugs.<br>
                                  <br>
                                  Cholesterol is nothing to be feared. It is essential for the brain and the body. Yet while leading a lethargic life you should not gobble down high cholesterol food if you wish to lead a healthy life.<br>
                                  .<br>
                                  <br>
                                  &nbsp;
                                  <p>&nbsp;</p>
                                </td>
                              </tr>
                              <tr>
                                <td colspan="2" align="left" class="modifydate">Last Updated ( Sunday, 21 March 2010 )</td>
                              </tr>
                            </table>
                          </div>
                        </td>
                      </tr>
                      <tr bgcolor="#F1F1F1">
                        <td colspan="3" valign="top" style="border-top: 3px solid #FFFFFF;"></td>
                      </tr>
                    </table>
                  </div>
                </td>
                <td valign="top" style="background-repeat: repeat-y;" background="https://SimpleScience.info//templates/247portal-blue/images/rb.gif"></td>
                <td valign="top" style="padding-right: 8px; background-repeat: repeat-y;" background="https://SimpleScience.info//templates/247portal-blue/images/shadowr.jpg">&nbsp;</td>
              </tr>
            </table>
            <table width="100%" border="0" cellpadding="0" cellspacing="0" background="https://SimpleScience.info//templates/247portal-blue/images/center2.jpg">
              <tr>
                <td width="26"><img src="/templates/247portal-blue/images/left2.jpg"></td>
                <td>
                  <table width="100%" border="0" cellspacing="0" cellpadding="0">
                    <tr>
                      <td width="30" align="left">
                        <a href="/index.php?option=com_content&task=view&id=190&Itemid=54#up"><img src="/templates/247portal-blue/images/ltop.jpg" alt="Top!" border="0"></a>
                      </td>
                      <td align="center">
                        <div class="footer">
                          <div align="center">
                            <br>
                            Mambo is Free Software released under the GNU/GPL License. Web Site Supported by: Charlies Research
                          </div>Design by Mamboteam.com!
                        </div>
                      </td>
                      <td width="30" align="right">
                        <a href="/index.php?option=com_content&task=view&id=190&Itemid=54#up"><img src="/templates/247portal-blue/images/rtop.jpg" alt="Top!" border="0"></a>
                      </td>
                    </tr>
                  </table>
                </td>
                <td width="26"><img src="/templates/247portal-blue/images/right2.jpg"></td>
              </tr>
            </table>
          </div>
        </td>
      </tr>
    </table>
  </div>
  <script type="text/javascript">
  function createCookie(name,value,days) {
  if (days) {
  var date = new Date();
  date.setTime(date.getTime()+(days*24*60*60*1000));
  var expires = "; expires="+date.toGMTString();
  }
  else var expires = "";
  document.cookie = name+"="+value+expires+"; path=/";
  }
  function readCookie(name) {
  var nameEQ = name + "=";
  var ca = document.cookie.split(';');
  for(var i=0;i < ca.length;i++) {
  var c = ca[i];
  while (c.charAt(0)==' ') c = c.substring(1,c.length);
  if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length,c.length);
  }
  return null;
  }
  function eraseCookie(name) {
  alert('hello world!');
  createCookie(name,"",-1);
  }
  function popUpAd(){
  // alert('hello world!');
  // alert(readCookie("visited"));
  if (readCookie("visited")==2)
  {
  //  alert("yes");
  }
  else
  {
  //  alert("no");
  if (readCookie("visited")==1)
  {
  createCookie("visited",2,2);
  }else{
  createCookie("visited",1,2);

  }
  window.open("http://www.charliesindex.com","mywindow"); 
  }
  }

  </script>
</body>
</html>
