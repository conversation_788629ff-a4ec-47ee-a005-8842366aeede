<?php
// Simple script to help extract content from HTML files
// Place this in the root directory and run via browser or command line

// Directory containing HTML files
$htmlDir = '.content.kU1kKF5p/html/';

// Function to extract content from HTML file
function extractContent($filePath) {
    $html = file_get_contents($filePath);
    
    // Extract title
    preg_match('/<span id="sites-page-title" dir="ltr"[^>]*>(.*?)<\/span>/s', $html, $titleMatches);
    $title = isset($titleMatches[1]) ? trim($titleMatches[1]) : 'Unknown Title';
    
    // Extract content
    preg_match('/<div dir="ltr">(.*?)<\/div>\s*<\/td>\s*<\/tr>/s', $html, $contentMatches);
    $content = isset($contentMatches[1]) ? trim($contentMatches[1]) : 'No content found';
    
    // Extract images
    preg_match_all('/<img src="([^"]+)"/s', $content, $imageMatches);
    $images = isset($imageMatches[1]) ? $imageMatches[1] : [];
    
    return [
        'title' => $title,
        'content' => $content,
        'images' => $images
    ];
}

// Example usage
$sampleFile = '.content.kU1kKF5p/html/7/7b84ada5679b1c507e8006026e672236e7723466.00000076.html';
$articleData = extractContent($sampleFile);

echo "<h1>Extracted Article</h1>";
echo "<h2>Title: " . htmlspecialchars($articleData['title']) . "</h2>";
echo "<h3>Content:</h3>";
echo "<div style='border:1px solid #ccc; padding:10px;'>" . $articleData['content'] . "</div>";
echo "<h3>Images:</h3>";
echo "<ul>";
foreach($articleData['images'] as $img) {
    echo "<li>" . htmlspecialchars($img) . "</li>";
}
echo "</ul>";